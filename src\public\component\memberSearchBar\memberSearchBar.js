"use strict";
Vue.component("member-search-bar", function (resolve, reject) {
  $.get("component/memberSearchBar/memberSearchBar.html").then(function (res) {
    resolve({
      template: res,
      props: {
        loginInfo: {
          type: Object,
        },
        showMemberSearch: {
          type: Boolean,
          default: false,
        },
      },
      data: function () {
        return {
          //loading页面转圈圈
          loading: false,
          phone: "",
          memberInfo: {
            pic: "", // 头像
            member_name: "", // 会员姓名
            remarks_name: "", // 备注名称
            sex: 0, // 性别 1=男 2=女
            is_vip: false, // 是否VIP
            phone: "", // 手机号
            member_number: "", // 会员编号
            total: 0, // 累计消费(分)
            count: 0, // 消费次数
            MaxNum: 0, // 剩余卡次
            balance: 0, // 剩余余额(分)
          },
        };
      },
      methods: {
        clearMemberInfo: function () {
          this.phone = "";
          // 使用$set确保响应式更新
          this.$set(this, "memberInfo", {
            pic: "", // 头像
            member_name: "", // 会员姓名
            remarks_name: "", // 备注名称
            sex: 0, // 性别 1=男 2=女
            is_vip: false, // 是否VIP
            phone: "", // 手机号
            member_number: "", // 会员编号
            total: 0, // 累计消费(分)
            count: 0, // 消费次数
            MaxNum: 0, // 剩余卡次
            balance: 0, // 剩余余额(分)
          });
          this.$emit("handle-clear");
        },
        //查询会员
        memberSearch: function (phone) {
          var _self = this;
          var keyword = phone;

          let loading = this.$loading({
            lock: true,
            text: "加载中...",
            spinner: "el-icon-loading",
            background: "rgba(255,255,255,0)",
          });
          $.ajax({
            url: window.baseUrl + "/android/vip/memberSearch",
            type: "post",
            data: {
              keyword: keyword,
              merchantid: _self.loginInfo.merchantid,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              _self.loading = false;
              if (res.code == 1 && res.data.length > 0) {
                // 使用$set确保响应式更新
                _self.$set(_self, "memberInfo", res.data[0]);
                loading.close();
                _self.$emit("handle-select-member", _self.memberInfo);
              } else {
                loading.close();
                if (phone.length == 11) {
                  _self.$message.error({
                    message: "未找到此会员信息",
                    duration: 1500,
                  });
                }
              }
            },
            error: function (error) {
              this.loading = false;
            },
          });
        },
        // 键盘等搜索按钮搜索事件
        billingInquiryEnter: function () {
          var _self = this;
          _self.serverPage = 1;
          _self.serviceList();
        },
        // 充次卡请求会员方法
        bindzhkInquireMember: function (phone) {
          if (phone.length == 11) {
            this.memberSearch(phone);
          }
        },
        //充次卡会员enter方法
        bindInquire: function (phone) {
          var reg = /^1(3|4|5|6|7|8|9)\d{9}$/;
          if (!phone) {
            this.$message.error({
              message: "输入手机号,登录会员",
              duration: 1500,
            });
          } else if (phone.length != "11" && !reg.test(phone)) {
            if (phone.length == 10) {
              this.loadEntityCard(phone);
            } else {
            }
          } else {
            this.memberSearch(phone);
          }
        },
        handleMemberSelect: function (memberInfo) {
          // 使用Vue.set确保响应式更新，或者使用$set
          this.$set(this, "memberInfo", {
            pic: memberInfo.pic || "",
            member_name: memberInfo.member_name || "",
            remarks_name: memberInfo.remarks_name || "",
            sex: memberInfo.sex || 0,
            is_vip: memberInfo.is_vip || false,
            phone: memberInfo.phone || "",
            member_number: memberInfo.member_number || "",
            total: memberInfo.total || 0,
            count: memberInfo.count || 0,
            MaxNum: memberInfo.MaxNum || 0,
            balance: memberInfo.balance || 0,
          });
          this.$emit("update:showMemberSearch", false);
          this.$emit("handle-select-member", memberInfo);
        },
        // 显示会员搜索
        showMemberSearchDialog: function () {
          this.$emit("update:showMemberSearch", true);
        },
        // 隐藏会员搜索
        hideMemberSearchDialog: function () {
          this.$emit("update:showMemberSearch", false);
        },
      },
      computed: {
        isShowMemberSearch: {
          get: function () {
            return this.showMemberSearch;
          },
          set: function (value) {
            this.$emit("update:showMemberSearch", value);
          },
        },
      },
      filters: {
        // 格式化充值金额/100
        filterMoney: function (money) {
          return (money / 100).toFixed(2);
        },
      },
      watch: {
        loginInfo: {
          handler: function (n) {
            this.userInfo = n;
          },
          deep: true,
          immediate: true,
        },
      },
    });
  });
});
