# BillPage DOM 操作重构总结

## 重构目标
将 billPage.js 中直接操作 DOM 的写法改为使用 Vue2 的 ref 操作方式，提高代码的可维护性和 Vue 兼容性。

## 主要变更

### 1. HTML 模板更新
- 为关键容器添加了 ref 属性：
  - `ref="cardWrap"` - 左侧卡片容器
  - `ref="rightScrollContainer"` - 右侧滚动容器
  - `ref="serviceCardsList"` - 服务卡片列表

### 2. DOM 查询方法重构

#### 替换前（直接 DOM 操作）
```javascript
queryInComponent: function (selector) {
  return this.$el.querySelectorAll(selector) || [];
}
```

#### 替换后（Vue ref 操作）
```javascript
// 获取服务卡片元素
getServiceCards: function () {
  if (this.$refs.serviceCardsList && this.$refs.serviceCardsList.$el) {
    return this.$refs.serviceCardsList.$el.querySelectorAll('.o-service-card') || [];
  }
  return [];
},

// 获取搜索卡片元素
getSearchCardBoxes: function () {
  if (this.$refs.cardWrap) {
    return this.$refs.cardWrap.querySelectorAll('#searchCardBox') || [];
  }
  return [];
}
```

### 3. 滚动操作重构

#### 替换前
```javascript
const serviceCardsContainers = this.queryInComponent(".o-scrollbar");
const rightScrollContainer = serviceCardsContainers[1];
if (rightScrollContainer) {
  rightScrollContainer.scrollTo({
    top: 0,
    behavior: "smooth",
  });
}
```

#### 替换后
```javascript
if (this.$refs.rightScrollContainer) {
  this.$refs.rightScrollContainer.scrollTo({
    top: 0,
    behavior: "smooth",
  });
}
```

### 4. 事件处理重构

#### 替换前（jQuery 事件委托）
```javascript
$(document).on("mouseenter", ".o-price-select-tag", function () {
  $(this).closest(".o-service-card").addClass("price-tag-hovered");
});
```

#### 替换后（Vue 事件处理）
```html
<div
  class="o-price-select-tag"
  @mouseenter="handlePriceTagHover($event, true)"
  @mouseleave="handlePriceTagHover($event, false)"
>
```

```javascript
handlePriceTagHover: function (event, isEnter) {
  const serviceCard = event.target.closest('.o-service-card');
  if (serviceCard) {
    if (isEnter) {
      serviceCard.classList.add('price-tag-hovered');
    } else {
      serviceCard.classList.remove('price-tag-hovered');
    }
  }
}
```

## 重构的优势

1. **更好的 Vue 兼容性**：使用 Vue 的 ref 系统而不是直接 DOM 操作
2. **更清晰的代码结构**：明确的 ref 引用比通用的 DOM 查询更易理解
3. **更好的性能**：减少了 DOM 查询的开销
4. **更易维护**：Vue 的响应式系统能更好地管理组件状态
5. **类型安全**：ref 引用提供了更好的类型检查支持

## 影响的功能模块

1. **服务卡片滚动**：添加服务时滚动到顶部
2. **产品卡片滚动**：添加产品时滚动到顶部
3. **扣卡操作**：扣卡数量变更时的高亮和滚动
4. **价格标签悬停**：鼠标悬停时的样式变化
5. **服务卡片高亮**：操作时的视觉反馈

## 测试建议

1. 测试所有滚动功能是否正常工作
2. 验证价格标签悬停效果
3. 检查服务卡片的高亮动画
4. 确认扣卡操作的视觉反馈
5. 测试在不同浏览器中的兼容性

## 注意事项

- 保持了原有的功能逻辑不变
- 所有的动画和视觉效果都得到保留
- 错误处理机制保持一致
- 向后兼容性良好