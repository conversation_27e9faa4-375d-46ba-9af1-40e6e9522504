<!doctype html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <title>登录</title>
    <link rel="stylesheet" href="css/css-comment.css" />
    <link rel="stylesheet" href="vue/element/<EMAIL>" />
    <!-- <link rel="stylesheet" href="css/shouyingtai_kaidan.css" /> -->
    <link rel="stylesheet" href="css/0-login.css" />
    <link
      rel="stylesheet"
      href="http://at.alicdn.com/t/font_1156348_if4g6jkesri.css"
    />
    <style>
      .el-icon-paperclip:before {
        content: "\e77d";
      }

      .iconyuming {
        cursor: pointer;
        color: #3363ff;
      }

      .progressMask {
        position: fixed;
        top: 0;
        left: 0;
        background: rgba(0, 0, 0, 0.8);
        width: 100%;
        height: 100%;
      }

      .progress {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      .el-progress__text {
        color: #fff;
      }

      .seeDomainName {
        cursor: pointer;
        color: #ff5a42;
        font-size: 14px;
      }
      .f-main-body-border::after {
        position: absolute;
        top: 0;
        left: 0;
        box-sizing: border-box;
        border: 2px solid #ebeef7;
        width: 100vw;
        height: 100vh;
        pointer-events: none;
        content: "";
      }
    </style>
  </head>

  <body class="f-main-body-border" style="background-color: rgba(0, 0, 0, 0)">
    <div class="login-Wrap drag">
      <div id="app" v-cloak>
        <div class="bg_login" @keyup.enter.exact="keyLogin">
          <div class="input_login no-drag">
            <div>
              <!---->
              <div class="flex w-full justify-center">
                <div class="flex space-x-2">
                  <img
                    src="./images/logo.png"
                    alt="logo"
                    style="width: 76px; height: 21px"
                  />
                  <div class="bg-black/90 text-white rounded px-2 py-1 text-xs">
                    收银系统
                  </div>
                </div>
              </div>
              <div class="form-wrap">
                <el-form>
                  <el-form-item>
                    <el-alert
                      v-show="errTip"
                      style="line-height: 0"
                      :title="errTip"
                      type="error"
                      :closable="false"
                      effect="dark"
                    ></el-alert>
                  </el-form-item>
                  <el-form-item>
                    <div class="el-input el-input--prefix">
                      <input
                        ref="phone"
                        type="text"
                        autocomplete="off"
                        maxlength="11"
                        placeholder="请输入内容"
                        prefix=""
                        v-model.trim="telephone"
                        @input="telInput(telephone)"
                        class="el-input__inner"
                      />
                      <span class="el-input__prefix">
                        <span class="login-label">手机号</span>
                      </span>
                    </div>
                  </el-form-item>
                  <el-form-item style="margin-bottom: 10px">
                    <div class="el-input el-input--prefix el-input--suffix">
                      <input
                        ref="password"
                        v-model.trim="password"
                        type="password"
                        autocomplete="off"
                        placeholder="请输入内容"
                        suffix=""
                        @input="telInput(telephone)"
                        prefix=""
                        class="el-input__inner"
                      />
                      <span class="el-input__prefix">
                        <span class="login-label">密码</span>
                      </span>
                      <span class="el-input__suffix">
                        <span class="el-input__suffix-inner">
                          <span class="login-label" style="text-align: center">
                            <i
                              @click="bindeEye(1)"
                              v-if="eye"
                              class="iconfont iconpassword2"
                            ></i>
                            <i
                              @click="bindeEye(0)"
                              v-else
                              class="iconfont iconpassword"
                            ></i>
                          </span>
                        </span>
                      </span>
                    </div>
                  </el-form-item>
                </el-form>
              </div>
              <div style="text-align: right; margin-bottom: 10px">
                <el-checkbox v-model="remember">记住账号</el-checkbox>
                <i
                  class="iconfont iconyuming"
                  title="查看域名"
                  @click="seeDomainName"
                ></i>
              </div>
              <el-button
                class="login"
                type="primary"
                @click="login"
                :disabled="loginLoad"
                :loading="loginLoad"
              >
                登&emsp;录
              </el-button>
            </div>
          </div>
        </div>
        <i class="el-icon-close close no-drag" @click="closeLogin"></i>

        <div class="version no-drag flex w-full justify-center space-x-4">
          <div>版本：V{{version}}</div>
          <div
            class="text-gray-500 cursor-pointer underline hover:text-primary"
            @click="isShowUpdataLog=true"
          >
            查看更新日志
          </div>
        </div>
        <el-dialog
          class="newVersion"
          :visible.sync="centerDialogVisible"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          :show-close="true"
          width="400px"
          center
        >
          <div
            style="
              font-weight: bold;
              font-size: 22px;
              text-align: center;
              margin-top: 5px;
            "
          >
            发现新版本
          </div>
          <div
            style="
              width: 100%;
              margin-top: 18px;
              display: flex;
              justify-content: center;
              color: #6b7280;
              font-size: 14px;
            "
          >
            <div style="margin-right: 4px">V{{version}}</div>
            <i class="el-icon-right" style="margin-right: 4px"></i>
            <div>V{{newVersion}}</div>
          </div>

          <div v-if="updateLogUrl">
            <div style="font-weight: bold; margin-bottom: 4px">更新内容：</div>
            <iframe
              class="o-scrollbar"
              :src="updateLogUrl"
              style="
                width: 100%;
                height: 170px;
                border: none;
                background: transparent;
              "
            >
            </iframe>
          </div>
          <div slot="footer" class="dialog-footer">
            <div>
              <el-button
                type="primary"
                @click="updateImmediately"
                :loading="progress"
              >
                立即更新
              </el-button>
            </div>
          </div>
        </el-dialog>
        <el-dialog
          :visible.sync="isShowUpdataLog"
          :close-on-click-modal="true"
          :close-on-press-escape="true"
          :show-close="true"
          width="400px"
          center
        >
          <div
            style="
              font-weight: bold;
              font-size: 20px;
              text-align: center;
              margin-top: 5px;
              margin-bottom: 6px;
            "
          >
            更新日志
          </div>
          <iframe
            class="o-scrollbar"
            :src="updateLogUrl"
            style="
              width: 100%;
              height: 252px;
              border: none;
              background: transparent;
            "
          >
          </iframe>
          <div slot="footer" class="dialog-footer"></div>
        </el-dialog>
        <el-dialog
          :visible.sync="isShowMustInstallLog"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          :show-close="false"
          width="400px"
          center
        >
          <div
            style="
              font-weight: bold;
              font-size: 20px;
              text-align: center;
              margin-top: 5px;
              margin-bottom: 6px;
            "
          >
            版本过低，请下载最新安装包！
          </div>
          <div slot="footer" class="dialog-footer"></div>
        </el-dialog>
        <div class="progressMask" v-if="progress"></div>
        <el-progress
          class="progress"
          v-if="progress"
          type="circle"
          :percentage="downloadProgress"
          :key="downloadProgress"
          color="#3363FF"
          :show-text="true"
        ></el-progress>
      </div>
    </div>
  </body>
  <script src="js/plugin/<EMAIL>"></script>
  <script src="vue/vue2.5.16.js"></script>
  <script src="vue/element/<EMAIL>"></script>
  <script src="js/plugin/jquery-3.2.1.min.js"></script>
  <!-- <script src="https://unpkg.com/hotkeys-js/dist/hotkeys.min.js"></script> -->
  <script>
    try {
      var gui = require("nw.gui");
      var win = gui.Window.get();
      var request = require("request");
      var fs = require("fs");
      win.show();
    } catch (e) {}

    const defaultSize = {
      w: 1200,
      h: 750,
    };
    function openCashier() {
      nw.Window.open(
        "./public/header.html",
        {
          // fullscreen: true,
          position: "center",
          frame: false,
          width: 1300,
          height: 768,
          min_width: 1200,
          min_height: 750,
        },
        function (win) {
          window.close();
          win.on("closed", function () {
            win = null;
          });
        }
      );
    }

    var app = new Vue({
      el: "#app",
      data() {
        const telephone = localStorage.getItem("loginInfo_telephone");
        // const telephone = "13809217687";
        return {
          MUST_INSTALL_VERSION: "0.3.0",
          telephone: telephone ? telephone : "",
          // password: "217687",
          password: "",
          errTip: "",
          eye: true,
          domainName: "",
          url: "",
          loginLoad: false,
          loginInfo: {},
          logo: {},
          // 版本
          updater: null,
          gui: null,
          downloadProgress: 0,
          version: "",
          newVersion: "",
          progress: false,
          timer: null,
          desc: "",
          updateLogHtml: null,
          updateLogUrl: null,
          centerDialogVisible: false,
          // 记住账号
          remember: telephone ? true : false,
          isShowUpdataLog: false,
          downloadUrl: "",
          mustInstallUrl: "",
          isShowMustInstallLog: false,
        };
      },
      mounted: function () {
        this.getLoginInfo();
        this.getFouce(this.$refs.phone);
        this.getdomainName();
        // 延迟检查更新，确保域名已设置
        this.$nextTick(() => {
          setTimeout(() => {
            if (this.domainName) {
              this.checkUpdate();
            } else {
              console.warn("域名未设置，跳过更新检查");
            }
          }, 1000);
        });
        this.getLogo();
      },

      beforeDestroy: function () {
        // 清理定时器
        if (this.timer) {
          window.clearInterval(this.timer);
          this.timer = null;
        }
        // 清理更新超时定时器
        if (this.updateTimeout) {
          clearTimeout(this.updateTimeout);
          this.updateTimeout = null;
        }
      },
      methods: {
        getdomainName() {
          var self = this;
          var d = localStorage.getItem("fdb-domainName");
          if (d) {
            self.domainName = d;
          } else {
            self.domainName = "https://store.fsyxgkj.com";
            localStorage.setItem("fdb-domainName", self.domainName);
          }
          self.url = self.domainName + "/index.php?s=";
          self.updateLogUrl =
            self.domainName +
            "/pcUpdate/cashierPc/updataLog.html?v=" +
            Date.now();
        },

        // 查看域名
        seeDomainName: function () {
          var self = this;
          self
            .$prompt("", "设置域名", {
              inputValue: self.domainName,
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              placeholder: "例如：http://www.baidu.com",
              // inputPattern:
              //   /^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/,
              // inputErrorMessage: "域名输入有误",
              closeOnClickModal: false,
            })
            .then(({ value }) => {
              self.domainName = value;
              // self.setDomainName(self.domainName);
              localStorage.setItem("fdb-domainName", self.domainName);
              self.getdomainName();
            })
            .catch(function () {
              //取消
            });
        },

        closeLogin: function () {
          win.close();
        },

        getLoginInfo: function () {
          if (localStorage.getItem("loginInfo")) {
            this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"));
          }
          // TODO 方便调试
        },

        getLogo: function (flag) {
          var _self = this;
          if (!flag) {
            let LoginLogoUrl = localStorage.getItem("LoginLogoUrl");
            // console.log(LoginLogoUrl);
            if (LoginLogoUrl) {
              return (_self.logo = JSON.parse(LoginLogoUrl));
            }
          }
          $.ajax({
            url: _self.url + "/android/Login/logo",
            type: "post",
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.logo = res.data;
                localStorage.setItem("LoginLogoUrl", JSON.stringify(res.data));
              }
            },
          });
        },

        bindeEye: function (type) {
          switch (type) {
            case 0:
              this.eye = true;
              this.$refs.password.type = "password";
              break;
            case 1:
              this.eye = false;
              this.$refs.password.type = "text";
              break;
          }
        },

        getFouce: function (dow) {
          this.$nextTick(
            function () {
              dow.focus();
            }.bind(this)
          );
        },

        telInput: function (val) {
          if (val != "" || val == 11) {
            this.errTip = "";
          }
        },

        login: function () {
          // TODO 方便调试
          // openCashier();
          var reg = /^1(3|4|5|6|7|8|9)\d{9}$/;
          if (!this.telephone) {
            this.errTip = "手机号或密码不能为空";
            this.getFouce(this.$refs.phone);
          } else if (!reg.test(this.telephone)) {
            this.errTip = "手机号码输入有误";
            this.getFouce(this.$refs.phone);
          } else if (!this.password) {
            this.errTip = "请输入正确密码";
            this.getFouce(this.$refs.password);
          } else {
            this.loginLoad = true;
            this.subLogin();
          }
        },

        keyLogin: function () {
          this.login();
        },

        // 登录
        subLogin: function () {
          var self = this;
          let ajaxTimeOut = $.ajax({
            url: self.url + "/android/Login/login",
            type: "post",
            data: {
              phone: self.telephone,
              pass: self.password,
            },
            timeout: 10000,
            success: function (res) {
              //   // var res = JSON.parse(res);
              if (res.code == 1) {
                localStorage.setItem("loginInfo", JSON.stringify(res.data));
                if (self.remember) {
                  localStorage.setItem("loginInfo_telephone", self.telephone);
                } else {
                  localStorage.removeItem("loginInfo_telephone");
                }
                if (res.data.technicianName) {
                  localStorage.setItem(
                    "globalTechnicianName",
                    res.data.technicianName
                  );
                } else {
                  localStorage.removeItem("globalTechnicianName");
                }
                // location.href = "cashier_system.html";
                openCashier();
                self.telephone = self.password = self.errTip = "";
                self.loginLoad = false;

                if (
                  res.data.pay_success_open == 1 &&
                  res.data.is_edit_authority == 1
                ) {
                  global.login = 1;
                } else {
                  global.login = 0;
                }
              } else {
                self.errTip = res.msg;
                self.password = "";
                self.loginLoad = false;
              }
            },
            complete: function (XMLHttpRequest, status) {
              //当请求完成时调用函数
              if (status == "timeout") {
                //status == 'timeout'意为超时,status的可能取值：success,notmodified,nocontent,error,timeout,abort,parsererror
                ajaxTimeOut.abort(); //取消请求
                self.loginLoad = false;
                self.$message({
                  type: "info",
                  message: "请求超时,请重试",
                  duration: 1500,
                });
              }
            },
          });
        },

        //立即更新
        updateImmediately() {
          // 判断是否为开发环境
          if (this.isDevelopmentEnvironment()) {
            this.$message({
              message: "当前为开发环境，不进行更新",
              type: "warning",
              duration: 3000,
            });
            this.centerDialogVisible = false;
            return;
          }

          // 验证下载URL
          if (!this.downloadUrl) {
            this.$message({
              message: "下载地址无效，无法进行更新",
              type: "error",
              duration: 3000,
            });
            this.centerDialogVisible = false;
            return;
          }

          console.log("开始立即更新流程");
          this.centerDialogVisible = false;
          this.progress = true;

          // 重置进度条
          if (this.timer) {
            window.clearInterval(this.timer);
            this.timer = null;
          }

          // 强制重置进度条
          this.$nextTick(() => {
            this.downloadProgress = 0;
          });

          // 添加整体超时处理
          var updateTimeout = setTimeout(() => {
            if (this.progress) {
              this.progress = false;
              this.$message({
                message: "更新超时，请稍后重试",
                type: "error",
                duration: 3000,
              });
            }
          }, 360000); // 6分钟总超时

          // 保存超时引用以便清理
          this.updateTimeout = updateTimeout;

          // 先尝试带进度的更新，如果失败则使用内置下载
          this.update();
        },

        // 判断是否为开发环境
        isDevelopmentEnvironment() {
          try {
            // 输出当前运行目录

            // 检测路径是否包含"cashier-pc"字符串
            if (process.cwd().includes("cashier-pc")) {
              console.log("当前运行目录:", process.cwd());
              return true;
            }

            // 方法1: 检查是否存在开发环境标识
            if (
              typeof process !== "undefined" &&
              process.env &&
              process.env.NODE_ENV === "development"
            ) {
              return true;
            }

            // 方法2: 检查NW.js的开发者工具是否可用
            if (typeof nw !== "undefined" && nw.Window && nw.Window.get()) {
              const win = nw.Window.get();
              // 如果可以打开开发者工具，通常表示是开发环境
              if (
                win.isDevToolsOpen &&
                typeof win.isDevToolsOpen === "function"
              ) {
                return true;
              }
            }

            // 方法3: 检查是否在本地开发服务器环境
            if (
              location.hostname === "localhost" ||
              location.hostname === "127.0.0.1"
            ) {
              return true;
            }

            // 方法4: 检查域名是否包含开发环境标识
            if (
              this.domainName &&
              (this.domainName.includes("localhost") ||
                this.domainName.includes("127.0.0.1") ||
                this.domainName.includes("192.168.") ||
                this.domainName.includes("dev") ||
                this.domainName.includes("test"))
            ) {
              return true;
            }

            return false;
          } catch (e) {
            console.warn("检查开发环境时出错:", e);
            return false;
          }
        },

        downloadNewVersionWithProgress(url, toUrl, cb) {
          var downloadApi = url;
          var filePath = toUrl;
          var stream = fs.createWriteStream(filePath);
          var self = this;

          console.log("开始带进度下载:", downloadApi);

          // 添加超时处理
          var downloadTimeout = setTimeout(function () {
            if (self.timer) {
              window.clearInterval(self.timer);
              self.timer = null;
            }
            stream.destroy();
            try {
              if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
              }
            } catch (e) {
              // 忽略清理错误
            }
            cb(new Error("下载超时"));
          }, 120000); // 2分钟超时

          request.head(
            downloadApi,
            { timeout: 10000 },
            function (err, res, body) {
              if (err) {
                clearTimeout(downloadTimeout);
                console.error("获取文件头信息失败:", err);
                return cb(err);
              }

              if (!res.headers["content-length"]) {
                clearTimeout(downloadTimeout);
                console.warn("无法获取文件大小，使用备用下载方式");
                return cb(new Error("无法获取文件大小"));
              }

              var totalSize = parseInt(res.headers["content-length"]);
              console.log("文件总大小:", totalSize, "bytes");

              self.timer = setInterval(function () {
                try {
                  if (fs.existsSync(filePath)) {
                    var currentSize = fs.statSync(filePath).size;
                    var progressPercentage = (currentSize / totalSize) * 100;
                    var newProgress = Math.min(
                      parseInt(progressPercentage),
                      99
                    ); // 最大99%，完成时设为100%

                    // 强制 Vue 更新进度条
                    self.$nextTick(function () {
                      self.downloadProgress = newProgress;
                    });
                  }
                } catch (e) {
                  // 忽略文件读取错误
                }
              }, 500);

              var downloadRequest = request(downloadApi, { timeout: 30000 });

              downloadRequest
                .pipe(stream)
                .on("close", function () {
                  clearTimeout(downloadTimeout);
                  if (self.timer) {
                    window.clearInterval(self.timer);
                    self.timer = null;
                  }

                  // 验证文件是否完整下载
                  try {
                    var finalSize = fs.statSync(filePath).size;
                    if (finalSize !== totalSize) {
                      console.warn(
                        "文件大小不匹配:",
                        finalSize,
                        "vs",
                        totalSize
                      );
                    }
                  } catch (e) {
                    console.warn("无法验证文件大小:", e);
                  }

                  // 强制更新到100%并触发重新渲染
                  self.$nextTick(function () {
                    self.downloadProgress = 100;
                  });

                  console.log("带进度下载完成");
                  cb(null, filePath);
                })
                .on("error", function (err) {
                  clearTimeout(downloadTimeout);
                  if (self.timer) {
                    window.clearInterval(self.timer);
                    self.timer = null;
                  }

                  console.error("下载过程中出错:", err);

                  // 清理下载失败的文件
                  try {
                    if (fs.existsSync(filePath)) {
                      fs.unlinkSync(filePath);
                    }
                  } catch (e) {
                    // 忽略清理错误
                  }
                  cb(err);
                });

              // 处理请求错误
              downloadRequest.on("error", function (err) {
                clearTimeout(downloadTimeout);
                if (self.timer) {
                  window.clearInterval(self.timer);
                  self.timer = null;
                }
                console.error("请求错误:", err);
                cb(err);
              });
            }
          );
        },

        update: function () {
          var self = this;
          var path = require("path");
          var downloadPath = path.join(process.cwd(), "updapp.zip");

          console.log("开始更新，下载URL:", self.downloadUrl);

          // 清理可能存在的旧文件
          try {
            if (fs.existsSync(downloadPath)) {
              fs.unlinkSync(downloadPath);
              console.log("清理了旧的下载文件");
            }
          } catch (e) {
            console.warn("清理旧文件失败:", e);
          }

          // 使用带进度的下载函数
          this.downloadNewVersionWithProgress(
            self.downloadUrl,
            downloadPath,
            function (err, filePath) {
              if (err) {
                console.warn("带进度下载失败，尝试使用内置下载:", err);
                // 如果带进度的下载失败，尝试使用hupdater内置下载
                self.updateWithBuiltinDownload();
                return;
              }

              console.log("带进度下载完成，开始解压");

              try {
                // 验证文件是否存在且有内容
                if (
                  !fs.existsSync(filePath) ||
                  fs.statSync(filePath).size === 0
                ) {
                  throw new Error("下载的文件无效或为空");
                }

                // 使用hupdater的解压功能，解压到当前目录
                self.updater.unpackNewVersion(filePath, function (err) {
                  if (err) {
                    console.error("解压失败:", err);
                    self.progress = false;
                    self.$message({
                      message:
                        "解压更新包失败: " +
                        (typeof err === "string" ? err : err.message),
                      type: "error",
                      duration: 3000,
                    });
                    return;
                  }

                  console.log("解压完成，准备重启");

                  self.$message({
                    message: "更新完成，5秒后将重启程序...",
                    type: "success",
                    duration: 5000,
                  });

                  // 延迟关闭进度条
                  setTimeout(function () {
                    self.progress = false;
                  }, 1000);

                  // 重启应用 - 传入正确的App参数
                  setTimeout(function () {
                    try {
                      self.updater.restartApp(self.gui.App);
                    } catch (e) {
                      console.error("重启应用失败:", e);
                      self.$message({
                        message: "请手动重启应用",
                        type: "warning",
                        duration: 3000,
                      });
                    }
                  }, 5000);
                });
              } catch (e) {
                console.error("更新过程出错:", e);
                self.progress = false;
                self.$message({
                  message: "更新过程出错: " + e.message,
                  type: "error",
                  duration: 3000,
                });
              }
            }
          );
        },

        checkUpdate() {
          var self = this;

          try {
            self.gui = require("nw.gui");
            var path = require("path");
            self.updater = require("hupdater");
            var pkg = require("../package");

            // 获取当前版本号
            var localVersion = pkg.version;
            self.version = localVersion;

            // 检测更新API地址
            self.versionCheckApi =
              self.domainName +
              "/pcUpdate/cashierPc/package.json?v=" +
              Date.now();

            // 开始检查版本号
            self.updater.checkVersion(
              self.versionCheckApi,
              function (err, newVersionData) {
                if (err) {
                  console.warn("检查更新失败:", err);
                  return;
                }
                console.log("服务器版本信息:", newVersionData);

                if (!newVersionData || !newVersionData.version) {
                  console.warn("服务器返回的版本信息格式错误");
                  return;
                }

                var mustInstall = self.updater.ifNeedUpdate(
                  localVersion,
                  self.MUST_INSTALL_VERSION
                );

                if (mustInstall) {
                  // 判断强制需要手动下载安装包重新安装的版本
                  self.isShowMustInstallLog = true;
                  self.mustInstallUrl =
                    newVersionData.mustInstallUrl + "?v=" + Date.now();

                  // 调用另存为文件，下载 mustInstallUrl 地址的文件
                  if (self.mustInstallUrl) {
                    try {
                      // 使用NW.js Shell API打开默认浏览器下载
                      const gui = require("nw.gui");
                      gui.Shell.openExternal(self.mustInstallUrl);
                    } catch (e) {
                      console.error("打开下载链接失败:", e);
                      // 备用方案：直接在当前窗口打开下载链接
                      window.open(self.mustInstallUrl, "_blank");
                    }
                  }
                } else {
                  // 判断是否需要更新
                  var ifUpdate = self.updater.ifNeedUpdate(
                    localVersion,
                    newVersionData.version
                  );
                  if (ifUpdate) {
                    self.downloadUrl =
                      newVersionData.downloadUrl + "?v=" + Date.now();
                    self.mustInstallUrl = newVersionData.mustInstallUrl;
                    self.centerDialogVisible = true;
                    self.desc = newVersionData.desc;
                    self.newVersion = newVersionData.version;
                  }
                }
              }
            );
          } catch (e) {
            console.error("初始化更新检查失败:", e);
            // 如果hupdater模块不存在或初始化失败，静默处理
          }
        },

        // 使用hupdater内置下载功能的备用更新方法
        updateWithBuiltinDownload: function () {
          var self = this;
          var path = require("path");
          var downloadPath = path.join(process.cwd(), "updapp.zip");

          console.log("开始使用内置下载功能");

          // 显示不确定进度
          self.$nextTick(function () {
            self.downloadProgress = 30;
          });

          // 添加超时处理
          var downloadTimeout = setTimeout(function () {
            self.progress = false;
            self.$message({
              message: "下载超时，请检查网络连接后重试",
              type: "error",
              duration: 3000,
            });
          }, 60000); // 60秒超时

          // 使用hupdater内置的下载功能
          self.updater.downloadNewVersion(
            self.downloadUrl,
            downloadPath,
            function (err) {
              // 清除超时定时器
              clearTimeout(downloadTimeout);

              if (err) {
                console.error("内置下载失败:", err);
                self.progress = false;
                self.$message({
                  message: "内置下载失败: " + (err.message || err),
                  type: "error",
                  duration: 3000,
                });
                return;
              }

              console.log("内置下载完成，开始解压");
              self.$nextTick(function () {
                self.downloadProgress = 80;
              });

              // 验证下载的文件是否存在
              if (!fs.existsSync(downloadPath)) {
                self.progress = false;
                self.$message({
                  message: "下载的文件不存在，更新失败",
                  type: "error",
                  duration: 3000,
                });
                return;
              }

              // 下载完成，开始解压
              self.updater.unpackNewVersion(downloadPath, function (err) {
                if (err) {
                  console.error("解压失败:", err);
                  self.progress = false;
                  self.$message({
                    message:
                      "解压更新包失败: " +
                      (typeof err === "string" ? err : err.message),
                    type: "error",
                    duration: 3000,
                  });
                  return;
                }

                console.log("解压完成，准备重启");
                self.$nextTick(function () {
                  self.downloadProgress = 100;
                });

                self.$message({
                  message: "更新完成，5秒后将重启程序...",
                  type: "success",
                  duration: 5000,
                });

                // 延迟关闭进度条
                setTimeout(function () {
                  self.progress = false;
                }, 1000);

                // 重启应用
                setTimeout(function () {
                  try {
                    self.updater.restartApp(self.gui.App);
                  } catch (e) {
                    console.error("重启应用失败:", e);
                    self.$message({
                      message: "请手动重启应用",
                      type: "warning",
                      duration: 3000,
                    });
                  }
                }, 5000);
              });
            }
          );
        },
      },
    });
  </script>
</html>
